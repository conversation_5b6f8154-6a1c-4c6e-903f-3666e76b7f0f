# Easy Social Share

A comprehensive Flutter plugin that enables seamless sharing of text, images, files, and multimedia content to various social media platforms and messaging apps.

## Features

### Supported Platforms
- **WhatsApp**: Share text messages and files (single or multiple)
- **Telegram**: Share text messages and files (single or multiple)
- **Twitter**: Share text messages with optional file attachments
- **Instagram**:
  - Direct messages
  - Feed posts (single or multiple images)
  - Reels (videos)
  - Stories (with customizable backgrounds and stickers)
- **Facebook**:
  - Feed posts with hashtags and files
  - Stories (with customizable backgrounds and stickers)
- **TikTok**: Share content to TikTok status (Android) or posts (iOS)
- **Messenger**: Share text messages
- **SMS**: Share text messages and files (single or multiple)
- **System Share**: Use the native platform share dialog
- **Clipboard**: Copy text to clipboard

### Cross-Platform Features
- **Get Installed Apps**: Check which social media apps are installed on the device
- **Platform-specific APIs**: Optimized methods for Android and iOS with different capabilities

## Platform Support

| Platform | Supported | File Sharing | Multiple Files |
|----------|-----------|--------------|----------------|
| Android  | ✅        | ✅           | ✅             |
| iOS      | ✅        | ⚠️ Limited   | ❌             |

## Installation

Add this to your package's `pubspec.yaml` file:

```yaml
dependencies:
  easy_social_share: ^0.3.2
```

Then run:

```bash
flutter pub get
```

## Quick Start

```dart
import 'package:easy_social_share/easy_social_share.dart';

class MyApp extends StatefulWidget {
  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  EasySocialShare easySocialShare = EasySocialShare();

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      home: Scaffold(
        body: Center(
          child: ElevatedButton(
            child: Text("Share to WhatsApp"),
            onPressed: () async {
              // Check if WhatsApp is installed
              Map<String, bool> apps = await easySocialShare.getInstalledApps();

              if (apps['whatsapp'] == true) {
                // Share to WhatsApp (Android)
                await easySocialShare.android.shareToWhatsapp(
                  "Hello from Easy Social Share!",
                  "/path/to/image.jpg"
                );
              }
            },
          ),
        ),
      ),
    );
  }
}
```

## Usage

### Basic Setup

```dart
import 'package:easy_social_share/easy_social_share.dart';

EasySocialShare easySocialShare = EasySocialShare();
```

### Check Installed Apps

```dart
Map<String, bool> apps = await easySocialShare.getInstalledApps();
print('WhatsApp installed: ${apps['whatsapp']}');
print('Instagram installed: ${apps['instagram']}');
```

### Platform-Specific Usage

The plugin provides platform-specific APIs to handle differences between Android and iOS:

#### Android Usage
Access methods via `easySocialShare.android.methodName()`

```dart
// Share to WhatsApp with image
await easySocialShare.android.shareToWhatsapp(
  "Check out this image!",
  "/path/to/image.jpg"
);

// Share multiple files to WhatsApp
await easySocialShare.android.shareFilesToWhatsapp([
  "/path/to/image1.jpg",
  "/path/to/image2.jpg",
]);

// Share to Instagram Story with custom background
await easySocialShare.android.shareToInstagramStory(
  'your_app_id',
  backgroundImage: '/path/to/background.jpg',
  stickerImage: '/path/to/sticker.png',
  backgroundTopColor: '#FF0000',
  backgroundBottomColor: '#00FF00',
);
```

#### iOS Usage
Access methods via `easySocialShare.iOS.methodName()`

```dart
// Share text to WhatsApp
await easySocialShare.iOS.shareToWhatsapp("Hello!");

// Share image to WhatsApp (separate method for iOS)
await easySocialShare.iOS.shareImageToWhatsApp("/path/to/image.jpg");

// Share to system dialog with multiple files
await easySocialShare.iOS.shareToSystem(
  "Check out these files!",
  filePaths: ["/path/to/file1.jpg", "/path/to/file2.pdf"]
);
```

## API Reference

### Basic Usage

```dart
import 'package:easy_social_share/easy_social_share.dart';

EasySocialShare easySocialShare = EasySocialShare();

// Check installed apps
Map<String, bool> apps = await easySocialShare.getInstalledApps();

// Share to WhatsApp (Android)
await easySocialShare.android.shareToWhatsapp("Hello!", "/path/to/image.jpg");

// Share to system (iOS)
await easySocialShare.iOS.shareToSystem("Hello!", filePaths: ["/path/to/image.jpg"]);
```

### Available Methods

#### Platform Usage

**Android**: Access methods via `easySocialShare.android.methodName()`
- Supports file sharing for most platforms
- Supports multiple file sharing for WhatsApp, Telegram, Instagram Feed, SMS, and System Share
- Includes TikTok Status sharing (Android-only feature)

**iOS**: Access methods via `easySocialShare.iOS.methodName()`
- Limited file sharing capabilities compared to Android
- WhatsApp requires separate methods for text (`shareToWhatsapp`) and images (`shareImageToWhatsApp`)
- Includes TikTok Post sharing (requires additional setup)
- No multiple file sharing support

#### Unified Methods Table

| Platform | Method | Android Parameters | iOS Parameters | Multiple Files Support |
|----------|--------|-------------------|----------------|----------------------|
| **Cross-Platform** | `getInstalledApps()` | - | - | Returns `Map<String, bool>` |
| **WhatsApp** | `shareToWhatsapp()` | message, filePath? | message (text only) | Android: `shareFilesToWhatsapp()` |
| | `shareImageToWhatsApp()` | ❌ | filePath | ❌ |
| **Telegram** | `shareToTelegram()` | message, filePath? | message (text only) | Android: `shareFilesToTelegram()` |
| **Twitter** | `shareToTwitter()` | message, filePath? | message, filePath? | ❌ |
| **Instagram Direct** | `shareToInstagramDirect()` | message | message | ❌ |
| **Instagram Feed** | `shareToInstagramFeed()` | message, filePath? | imagePath | Android: `shareFilesToInstagramFeed()` |
| **Instagram Reels** | `shareToInstagramReels()` | videoPaths (List) | videoPath (String) | ❌ |
| **Instagram Story** | `shareToInstagramStory()` | appId + story options* | appId + story options* | ❌ |
| **Facebook** | `shareToFacebook()` | hashtag, filePaths | hashtag, filePaths | ❌ |
| **Facebook Story** | `shareToFacebookStory()` | appId + story options* | appId + story options* | ❌ |
| **TikTok** | `shareToTiktokStatus()` | filePaths | ❌ (Android only) | ❌ |
| | `shareToTiktokPost()` | ❌ (iOS only) | videoFile, redirectUrl, fileType | ❌ |
| **Messenger** | `shareToMessenger()` | message | message | ❌ |
| **SMS** | `shareToSMS()` | message, filePath? | message (text only) | Android: `shareFilesToSMS()` |
| **System Share** | `shareToSystem()` | title, message, filePath? | message, filePaths? | Android: `shareFilesToSystem()` |
| **Clipboard** | `copyToClipBoard()` | message | message | ❌ |

*Story options: `stickerImage`, `backgroundImage`, `backgroundVideo`, `backgroundTopColor`, `backgroundBottomColor`, `attributionURL`

### Platform Differences

| Feature | Android | iOS |
|---------|---------|-----|
| File sharing | ✅ Most apps | ⚠️ Limited |
| Multiple files | ✅ | ❌ |
| TikTok Status | ✅ | ❌ |
| TikTok Post | ❌ | ✅ (requires setup) |

### Quick Examples

```dart
// Check if WhatsApp is installed
Map<String, bool> apps = await easySocialShare.getInstalledApps();
if (apps['whatsapp'] == true) {
  // Share to WhatsApp
}

// Share image to Instagram Story
await easySocialShare.android.shareToInstagramStory(
  'your_app_id',
  backgroundImage: '/path/to/image.jpg',
);

// Share multiple images (Android only)
await easySocialShare.android.shareFilesToWhatsapp([
  '/path/to/image1.jpg',
  '/path/to/image2.jpg',
]);
```

## Advanced Examples

### Instagram Story Sharing

```dart
// Share to Instagram Story with custom styling
await easySocialShare.android.shareToInstagramStory(
  'your_instagram_app_id', // Get this from Facebook Developer Console
  stickerImage: '/path/to/sticker.png',
  backgroundImage: '/path/to/background.jpg',
  backgroundTopColor: '#FF6B6B',
  backgroundBottomColor: '#4ECDC4',
  attributionURL: 'https://your-app.com',
);
```

### Multiple File Sharing (Android Only)

```dart
// Share multiple images to WhatsApp
await easySocialShare.android.shareFilesToWhatsapp([
  '/path/to/image1.jpg',
  '/path/to/image2.jpg',
  '/path/to/image3.jpg',
]);

// Share multiple files via system dialog
await easySocialShare.android.shareFilesToSystem(
  'Check out these files!',
  [
    '/path/to/document.pdf',
    '/path/to/image.jpg',
    '/path/to/video.mp4',
  ],
);
```

### Platform-Specific Conditional Sharing

```dart
import 'dart:io';

Future<void> shareContent(String message, String filePath) async {
  if (Platform.isAndroid) {
    // Android supports file sharing for most platforms
    await easySocialShare.android.shareToWhatsapp(message, filePath);
  } else if (Platform.isIOS) {
    // iOS requires separate methods for text and images
    await easySocialShare.iOS.shareToWhatsapp(message);
    if (filePath.isNotEmpty) {
      await easySocialShare.iOS.shareImageToWhatsApp(filePath);
    }
  }
}
```

## Platform Differences

### Android vs iOS Capabilities

| Feature | Android | iOS | Notes |
|---------|---------|-----|-------|
| File sharing | ✅ Most apps support files | ⚠️ Limited support | iOS has more restrictions |
| Multiple files | ✅ Supported | ❌ Not supported | Android only feature |
| TikTok Status | ✅ Supported | ❌ Not available | Android exclusive |
| TikTok Post | ❌ Not available | ✅ Supported | iOS exclusive |
| WhatsApp images | Combined method | Separate method required | Different API approaches |

### File Type Support

- **Images**: JPG, PNG, GIF (most platforms)
- **Videos**: MP4, MOV (Instagram Reels, TikTok, Stories)
- **Documents**: PDF, DOC, TXT (System Share, some messaging apps)
- **Multiple formats**: Supported on Android for compatible apps

## Requirements

- **Flutter**: >=3.32.4
- **Dart**: >=3.0.0 <4.0.0
- **Android**: API level 21+ (Android 5.0+)
- **iOS**: iOS 11.0+

## Setup

### Android Setup

Add the following to your `android/app/src/main/AndroidManifest.xml`:

```xml
<queries>
    <package android:name="com.whatsapp" />
    <package android:name="com.instagram.android" />
    <package android:name="com.facebook.katana" />
    <package android:name="com.twitter.android" />
    <package android:name="org.telegram.messenger" />
    <package android:name="com.zhiliaoapp.musically" />
    <package android:name="com.facebook.orca" />
</queries>
```

### iOS Setup

For TikTok sharing on iOS, add to your `ios/Runner/Info.plist`:

```xml
<key>LSApplicationQueriesSchemes</key>
<array>
    <string>whatsapp</string>
    <string>instagram</string>
    <string>instagram-stories</string>
    <string>fb</string>
    <string>fb-messenger</string>
    <string>twitter</string>
    <string>tg</string>
    <string>tiktokopensdk</string>
</array>
```

## Error Handling

```dart
try {
  String result = await easySocialShare.android.shareToWhatsapp(
    "Hello World!",
    "/path/to/image.jpg"
  );
  print('Share result: $result');
} catch (e) {
  print('Error sharing: $e');
  // Handle error appropriately
}
```

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

- **Repository**: [https://github.com/tentram/flutter_packages](https://github.com/tentram/flutter_packages)
- **Issues**: [Report issues here](https://github.com/tentram/flutter_packages/issues)
- **Example**: Check the [example](example/) directory for a complete implementation

## Changelog

### Version 0.3.2
- Current stable version
- Support for major social media platforms
- Cross-platform compatibility
- Multiple file sharing on Android

For detailed changelog, see [CHANGELOG.md](CHANGELOG.md).
